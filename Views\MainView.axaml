<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
             xmlns:helpers="clr-namespace:Lyxie_desktop.Helpers"
             mc:Ignorable="d" d:DesignWidth="1280" d:DesignHeight="760"
             x:Class="Lyxie_desktop.Views.MainView">

    <!-- 使用Border包装Grid以支持BoxShadow -->
    <Border Name="MainViewBorder" BoxShadow="inset 0 0 15 0 Transparent">
        <Grid>
            <!-- 添加微妙的背景渐变效果 -->
            <Grid.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="{DynamicResource AppBackgroundGradientStartColor}" Offset="0"/>
                    <GradientStop Color="{DynamicResource AppBackgroundGradientEndColor}" Offset="1"/>
                </LinearGradientBrush>
            </Grid.Background>

            <!-- 词云背景层 - 覆盖整个主界面 -->
            <Border Name="WordCloudContainer"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    ZIndex="1">
                <!-- 词云控件将在代码中动态添加 -->
            </Border>

        <!-- 设置按钮 -->
        <Button Name="SettingsButton"
                Width="40"
                Height="40"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Margin="20"
                Background="Transparent"
                BorderThickness="0"
                Cursor="Hand"
                ZIndex="3">
            <materialIcons:MaterialIcon Kind="Settings"
                                        Width="24"
                                        Height="24"
                                        Foreground="{DynamicResource IconBrush}" />

            <!-- 按钮悬停效果 -->
            <Button.Styles>
                <Style Selector="Button:pointerover">
                    <Setter Property="Background" Value="{DynamicResource ButtonHoverBackgroundBrush}"/>
                </Style>
                <Style Selector="Button:pressed">
                    <Setter Property="Background" Value="{DynamicResource ButtonHoverBackgroundBrush}"/>
                </Style>
            </Button.Styles>
        </Button>

        <!-- MCP服务状态指示器 -->
        <StackPanel Name="McpStatusPanel"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right"
                    VerticalAlignment="Top"
                    Margin="20"
                    ZIndex="3">

            <!-- 状态图标 -->
            <Border Name="McpStatusIcon"
                    Width="12"
                    Height="12"
                    CornerRadius="6"
                    Background="#FF6B6B"
                    Margin="0,0,8,0"
                    VerticalAlignment="Center">
                <!-- 状态指示器动画 -->
                <Border.Styles>
                    <Style Selector="Border.running">
                        <Setter Property="Background" Value="#4ECDC4"/>
                    </Style>
                    <Style Selector="Border.partial">
                        <Setter Property="Background" Value="#FFE66D"/>
                    </Style>
                    <Style Selector="Border.error">
                        <Setter Property="Background" Value="#FF6B6B"/>
                    </Style>
                    <Style Selector="Border.initializing">
                        <Setter Property="Background" Value="#95A5A6"/>
                    </Style>
                </Border.Styles>
            </Border>

            <!-- 状态文本 -->
            <TextBlock Name="McpStatusText"
                       Text="MCP未初始化"
                       FontSize="12"
                       Foreground="{DynamicResource TextBrush}"
                       VerticalAlignment="Center"
                       Opacity="0.8"/>

            <!-- 工具提示 -->
            <StackPanel.Styles>
                <Style Selector="StackPanel">
                    <Setter Property="ToolTip.Tip">
                        <Template>
                            <Border Background="{DynamicResource TooltipBackgroundBrush}"
                                    BorderBrush="{DynamicResource TooltipBorderBrush}"
                                    BorderThickness="1"
                                    CornerRadius="4"
                                    Padding="8">
                                <StackPanel>
                                    <TextBlock Name="McpTooltipTitle"
                                               Text="MCP服务状态"
                                               FontWeight="Bold"
                                               FontSize="12"
                                               Foreground="{DynamicResource TextBrush}"
                                               Margin="0,0,0,4"/>
                                    <TextBlock Name="McpTooltipDetails"
                                               Text="正在检查服务状态..."
                                               FontSize="11"
                                               Foreground="{DynamicResource TextBrush}"
                                               Opacity="0.8"
                                               TextWrapping="Wrap"
                                               MaxWidth="200"/>
                                </StackPanel>
                            </Border>
                        </Template>
                    </Setter>
                </Style>
            </StackPanel.Styles>
        </StackPanel>

        <!-- 扳手按钮 -->
        <Button Name="ToolButton"
                Width="40"
                Height="40"
                HorizontalAlignment="Left"
                VerticalAlignment="Bottom"
                Margin="20"
                Background="Transparent"
                BorderThickness="0"
                Cursor="Hand"
                ZIndex="3">
            <materialIcons:MaterialIcon Kind="Wrench"
                                        Width="24"
                                        Height="24"
                                        Foreground="{DynamicResource IconBrush}" />

            <!-- 按钮悬停效果 -->
            <Button.Styles>
                <Style Selector="Button:pointerover">
                    <Setter Property="Background" Value="{DynamicResource ButtonHoverBackgroundBrush}"/>
                </Style>
                <Style Selector="Button:pressed">
                    <Setter Property="Background" Value="{DynamicResource ButtonHoverBackgroundBrush}"/>
                </Style>
            </Button.Styles>
        </Button>

        <!-- 光晕效果层 -->
        <Grid HorizontalAlignment="Center" VerticalAlignment="Center" ZIndex="1">
            <!-- 外层光晕 -->
            <Border Name="OuterGlow"
                    Width="680"
                    Height="680"
                    CornerRadius="340"
                    Opacity="0.15"
                    IsHitTestVisible="False">
                <Border.Background>
                    <RadialGradientBrush>
                        <GradientStop Offset="0"/>
                        <GradientStop Offset="0.4"/>
                        <GradientStop Offset="0.8"/>
                        <GradientStop Offset="1"/>
                    </RadialGradientBrush>
                </Border.Background>
            </Border>

            <!-- 中层光晕 -->
            <Border Name="MiddleGlow"
                    Width="640"
                    Height="640"
                    CornerRadius="320"
                    Opacity="0.2"
                    IsHitTestVisible="False">
                <Border.Background>
                    <RadialGradientBrush>
                        <GradientStop Offset="0"/>
                        <GradientStop Offset="0.4"/>
                        <GradientStop Offset="0.8"/>
                        <GradientStop Offset="1"/>
                    </RadialGradientBrush>
                </Border.Background>
            </Border>

            <!-- 内层光晕 -->
            <Border Name="InnerGlow"
                    Width="620"
                    Height="620"
                    CornerRadius="310"
                    Opacity="0.25"
                    IsHitTestVisible="False">
                <Border.Background>
                    <RadialGradientBrush>
                        <GradientStop Offset="0"/>
                        <GradientStop Offset="0.4"/>
                        <GradientStop Offset="0.8"/>
                        <GradientStop Offset="1"/>
                    </RadialGradientBrush>
                </Border.Background>
            </Border>
        </Grid>

                <!-- 主界面对话按钮容器 - 模拟CSS .container -->
        <Grid HorizontalAlignment="Center" VerticalAlignment="Center" ZIndex="2">
            
            <!-- 背景光晕层 - 模拟CSS ::before伪元素 -->
            <Border Name="ButtonGlowBackground"
                    Width="620" Height="620"
                    CornerRadius="310"
                    IsHitTestVisible="False"
                    Opacity="0">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Offset="0"/>
                        <GradientStop Offset="0.4"/>
                        <GradientStop Offset="0.8"/>
                        <GradientStop Offset="1"/>
                    </LinearGradientBrush>
                </Border.Background>
                <Border.Effect>
                    <BlurEffect Radius="0"/>
                </Border.Effect>
            </Border>

            <!-- 边框容器 - 模拟CSS .container -->
            <Border Name="BorderContainer"
                    Width="612" Height="612"
                    CornerRadius="306"
                    Background="Transparent"
                    BorderThickness="6"
                    helpers:LinearGradientBrushHelper.RotateAngle="0">
                <Border.BorderBrush>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#8ddcff" Offset="0"/>
                        <GradientStop Color="#5c87e3" Offset="0.2"/>
                        <GradientStop Color="#3c67e3" Offset="0.5"/>
                        <GradientStop Color="#2e00c2" Offset="0.8"/>
                        <GradientStop Color="#1a0080" Offset="1"/>
                    </LinearGradientBrush>
                </Border.BorderBrush>

                <!-- 主按钮 -->
                <Button Name="MainCircleButton"
                        Width="600"
                        Height="600"
                        CornerRadius="300"
                        BorderThickness="0"
                        Background="Transparent"
                        Cursor="Hand"
                        helpers:LinearGradientBrushHelper.RotateAngle="0">
                    
                    <!-- 隐藏的边框渐变，用于动画系统 -->
                    <Button.BorderBrush>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                            <GradientStop Color="#8ddcff" Offset="0"/>
                            <GradientStop Color="#5c87e3" Offset="0.2"/>
                            <GradientStop Color="#3c67e3" Offset="0.5"/>
                            <GradientStop Color="#2e00c2" Offset="0.8"/>
                            <GradientStop Color="#1a0080" Offset="1"/>
                        </LinearGradientBrush>
                    </Button.BorderBrush>

                    <!-- 按钮样式 -->
                    <Button.Styles>
                        <Style Selector="Button:pointerover">
                            <Setter Property="Background">
                                <Setter.Value>
                                    <SolidColorBrush Color="Transparent" Opacity="0.1"/>
                                </Setter.Value>
                            </Setter>
                        </Style>
                        <Style Selector="Button:pressed">
                            <Setter Property="Background">
                                <Setter.Value>
                                    <SolidColorBrush Color="Transparent" Opacity="0.2"/>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Styles>

                    <!-- 文本内容 -->
                    <StackPanel HorizontalAlignment="Center"
                                VerticalAlignment="Center">
                        <TextBlock Name="AppNameTextBlock"
                                   Text="Lyxie"
                                   FontSize="24"
                                   FontWeight="Bold"
                                   Foreground="{DynamicResource PrimaryTextBrush}"
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,10"/>

                        <TextBlock Name="ClickToStartTextBlock"
                                   Text="点击开始对话"
                                   FontSize="16"
                                   Foreground="{DynamicResource SecondaryTextBrush}"
                                   HorizontalAlignment="Center"
                                   Opacity="0.9"/>
                    </StackPanel>
                </Button>
            </Border>
        </Grid>

        <!-- 浮动工具面板 - 响应式布局 -->
        <Border Name="ToolPanel"
                MinWidth="220"
                MaxWidth="300"
                HorizontalAlignment="Left"
                VerticalAlignment="Bottom"
                Margin="20,0,0,70"
                Background="{DynamicResource FloatingPanelBackgroundBrush}"
                BorderBrush="{DynamicResource FloatingPanelBorderBrush}"
                BorderThickness="1"
                CornerRadius="8"
                BoxShadow="0 4 12 0 #40000000"
                Opacity="0"
                IsHitTestVisible="True"
                ZIndex="10"
                IsVisible="False">

            <Grid Margin="15">
                <!-- 定义行：标题 + 三个开关行 -->
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" MinHeight="30"/>  <!-- 标题行 -->
                    <RowDefinition Height="Auto" MinHeight="36"/>  <!-- TTS开关 -->
                    <RowDefinition Height="Auto" MinHeight="36"/>  <!-- 开发功能1 -->
                    <RowDefinition Height="Auto" MinHeight="36"/>  <!-- 开发功能2 -->
                </Grid.RowDefinitions>
                
                <!-- 样式定义：确保ToggleSwitch可以正确接收点击事件 -->
                <Grid.Styles>
                    <Style Selector="ToggleSwitch">
                        <Setter Property="IsHitTestVisible" Value="True"/>
                    </Style>
                    <Style Selector="ToggleSwitch /template/ Border#OuterBorder">
                        <Setter Property="IsHitTestVisible" Value="True"/>
                        <Setter Property="Background" Value="{DynamicResource ButtonBackgroundBrush}"/>
                        <Setter Property="BorderThickness" Value="1"/>
                        <Setter Property="BorderBrush" Value="{DynamicResource ButtonBorderBrush}"/>
                    </Style>
                    <Style Selector="ToggleSwitch /template/ Thumb#Thumb">
                        <Setter Property="IsHitTestVisible" Value="True"/>
                    </Style>
                </Grid.Styles>

                <!-- 定义列：图标、文本、开关 -->
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>    <!-- 图标列 -->
                    <ColumnDefinition Width="*"/>       <!-- 文本列，自适应 -->
                    <ColumnDefinition Width="Auto"/>    <!-- 开关列 -->
                </Grid.ColumnDefinitions>

                <!-- 标题 -->
                <TextBlock Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="3"
                           Text="工具设置"
                           FontSize="14"
                           FontWeight="SemiBold"
                           Foreground="{DynamicResource PrimaryTextBrush}"
                           Margin="0,0,0,12"
                           HorizontalAlignment="Left"/>

                <!-- TTS开关行 -->
                <materialIcons:MaterialIcon Grid.Row="1" Grid.Column="0"
                                            Kind="VolumeHigh"
                                            Width="16"
                                            Height="16"
                                            Foreground="{DynamicResource SecondaryIconBrush}"
                                            VerticalAlignment="Center"
                                            Margin="0,4,8,4"/>
                <TextBlock Grid.Row="1" Grid.Column="1"
                           Text="TTS"
                           FontSize="12"
                           Foreground="{DynamicResource SecondaryTextBrush}"
                           VerticalAlignment="Center"
                           Margin="0,4,8,4"/>
                <ToggleSwitch Grid.Row="1" Grid.Column="2"
                              Name="TTSToggle"
                              OffContent=""
                              OnContent=""
                              Width="46"
                              Height="28"
                              IsHitTestVisible="True"
                              VerticalAlignment="Center"
                              HorizontalAlignment="Right"
                              Margin="0,4,0,4"/>

                <!-- 开发功能1开关行 -->
                <materialIcons:MaterialIcon Grid.Row="2" Grid.Column="0"
                                            Kind="Build"
                                            Width="16"
                                            Height="16"
                                            Foreground="{DynamicResource SecondaryIconBrush}"
                                            VerticalAlignment="Center"
                                            Margin="0,8,8,4"/>
                <TextBlock Grid.Row="2" Grid.Column="1"
                           Text="开发功能1"
                           FontSize="12"
                           Foreground="{DynamicResource SecondaryTextBrush}"
                           VerticalAlignment="Center"
                           Margin="0,8,8,4"/>
                <ToggleSwitch Grid.Row="2" Grid.Column="2"
                              Name="Dev1Toggle"
                              OffContent=""
                              OnContent=""
                              Width="46"
                              Height="28"
                              IsHitTestVisible="True" 
                              VerticalAlignment="Center"
                              HorizontalAlignment="Right"
                              Margin="0,8,0,4"/>

                <!-- 开发功能2开关行 -->
                <materialIcons:MaterialIcon Grid.Row="3" Grid.Column="0"
                                            Kind="FileTree"
                                            Width="16"
                                            Height="16"
                                            Foreground="{DynamicResource SecondaryIconBrush}"
                                            VerticalAlignment="Center"
                                            Margin="0,8,8,4"/>
                <StackPanel Grid.Row="3" Grid.Column="1" Orientation="Vertical" Margin="0,8,8,4">
                    <TextBlock Text="MCP 工具"
                              FontSize="12"
                              Foreground="{DynamicResource SecondaryTextBrush}"
                              VerticalAlignment="Center"/>
                    <TextBlock Text="文件系统"
                              FontSize="10"
                              Opacity="0.8"
                              Foreground="{DynamicResource SecondaryTextBrush}"
                              VerticalAlignment="Center"/>
                </StackPanel>
                <ToggleSwitch Grid.Row="3" Grid.Column="2"
                              Name="Dev2Toggle"
                              OffContent="未启用"
                              OnContent="已启用"
                              Content="未验证"
                              Width="46"
                              Height="28"
                              IsHitTestVisible="True"
                              VerticalAlignment="Center"
                              HorizontalAlignment="Right"
                              Margin="0,8,0,4"/>
            </Grid>
        </Border>

        <!-- AI对话界面容器 -->
        <Grid Name="ChatContainer"
              HorizontalAlignment="Stretch"
              VerticalAlignment="Stretch"
              IsVisible="False"
              Opacity="0"
              ZIndex="15">
            
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>    <!-- 侧边栏 -->
                <ColumnDefinition Width="*"/>      <!-- 主聊天区域 -->
            </Grid.ColumnDefinitions>

            <!-- 侧边栏 -->
            <Border Grid.Column="0"
                    Name="ChatSidebarContainer"
                    Background="Transparent"
                    Margin="20,20,0,20">
                <!-- 侧边栏控件将在代码中动态添加 -->
            </Border>

            <!-- 主聊天区域 -->
            <Grid Grid.Column="1" Name="MainChatArea">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>        <!-- 对话历史区域 -->
                    <RowDefinition Height="Auto"/>     <!-- 输入区域 -->
                </Grid.RowDefinitions>

                <!-- 对话历史区域 -->
                <Border Grid.Row="0"
                        Name="ChatHistoryArea"
                        Background="{DynamicResource ChatBackgroundBrush}"
                        CornerRadius="12,12,0,0"
                        Margin="0,0,20,0"
                        BoxShadow="0 2 10 0 #20000000">
                
                <!-- 顶部工具栏 -->
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 标题栏 -->
                    <Border Grid.Row="0"
                            Background="{DynamicResource ChatHeaderBackgroundBrush}"
                            CornerRadius="12,12,0,0"
                            Height="50">
                        <Grid>
                            <!-- 标题和API信息 -->
                            <StackPanel HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Orientation="Vertical">
                                <TextBlock Name="ChatTitleText"
                                           Text="Lyxie"
                                           FontSize="16"
                                           FontWeight="SemiBold"
                                           HorizontalAlignment="Center"
                                           Foreground="{DynamicResource PrimaryTextBrush}"/>
                                <TextBlock Name="ChatApiInfoText"
                                           Text="未配置 API"
                                           FontSize="11"
                                           HorizontalAlignment="Center"
                                           Foreground="{DynamicResource SecondaryTextBrush}"
                                           Opacity="0.8"/>
                            </StackPanel>
                            
                            <!-- 返回按钮 -->
                            <Button Name="ChatBackButton"
                                    Width="40"
                                    Height="40"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center"
                                    Margin="5"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Cursor="Hand">
                                <materialIcons:MaterialIcon Kind="ArrowBack"
                                                            Width="24"
                                                            Height="24"
                                                            Foreground="{DynamicResource IconBrush}"/>
                            </Button>
                            
                            <!-- 设置按钮 -->
                            <Button Name="ChatSettingsButton"
                                    Width="40"
                                    Height="40"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Center"
                                    Margin="5"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    Cursor="Hand">
                                <materialIcons:MaterialIcon Kind="Settings"
                                                            Width="24"
                                                            Height="24"
                                                            Foreground="{DynamicResource IconBrush}"/>
                            </Button>
                        </Grid>
                    </Border>
                    
                    <!-- 消息列表 -->
                    <ScrollViewer Grid.Row="1"
                                  Name="MessageScrollViewer"
                                  HorizontalScrollBarVisibility="Disabled"
                                  VerticalScrollBarVisibility="Auto">
                        <StackPanel Name="MessageList"
                                    Margin="15"
                                    Spacing="10">
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </Border>

                <!-- 输入区域 -->
                <Border Grid.Row="1"
                        Name="ChatInputArea"
                        Background="{DynamicResource ChatInputBackgroundBrush}"
                        CornerRadius="0,0,12,12"
                        Margin="0,0,20,0"
                        BoxShadow="0 -2 10 0 #20000000"
                        Height="0"
                        VerticalAlignment="Bottom">
                
                <Grid Margin="15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>       <!-- 输入框 -->
                        <ColumnDefinition Width="Auto"/>    <!-- 语音按钮 -->
                        <ColumnDefinition Width="Auto"/>    <!-- 发送按钮 -->
                    </Grid.ColumnDefinitions>
                    
                    <!-- 输入框 -->
                    <TextBox Grid.Column="0"
                             Name="MessageInput"
                             Watermark="输入您的消息..."
                             FontSize="14"
                             MinHeight="40"
                             MaxHeight="120"
                             TextWrapping="Wrap"
                             AcceptsReturn="False"
                             VerticalContentAlignment="Center"
                             Margin="0,0,10,0"/>
                    
                    <!-- 语音输入按钮 -->
                    <!-- 
                    <Button Grid.Column="1"
                            Name="VoiceInputButton"
                            Width="40"
                            Height="40"
                            Background="{DynamicResource SecondaryButtonBackgroundBrush}"
                            BorderThickness="0"
                            CornerRadius="20"
                            Cursor="Hand"
                            Margin="0,0,10,0">
                        <materialIcons:MaterialIcon Kind="Microphone"
                                                    Width="24"
                                                    Height="24"
                                                    Foreground="{DynamicResource IconBrush}"/>
                    </Button>
                    -->
                    
                    <!-- 发送按钮 -->
                    <Button Grid.Column="2"
                            Name="SendButton"
                            Width="40"
                            Height="40"
                            Background="{DynamicResource PrimaryButtonBackgroundBrush}"
                            BorderThickness="0"
                            CornerRadius="20"
                            Cursor="Hand">
                        <materialIcons:MaterialIcon x:Name="SendButtonIcon"
                                                    Kind="Send"
                                                    Width="24"
                                                    Height="24"
                                                    Foreground="{DynamicResource ButtonTextBrush}"/>
                    </Button>
                </Grid>
                </Border>
            </Grid>
        </Grid>
    </Grid>
</Border>

</UserControl>
