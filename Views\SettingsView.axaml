<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:materialIcons="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
             xmlns:vm="clr-namespace:Lyxie_desktop.Views"
             mc:Ignorable="d" d:DesignWidth="1280" d:DesignHeight="760"
             x:Class="Lyxie_desktop.Views.SettingsView"
             x:DataType="vm:SettingsView">

    <Grid>
        <!-- 添加微妙的背景渐变效果 -->
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="{DynamicResource AppBackgroundGradientStartColor}" Offset="0"/>
                <GradientStop Color="{DynamicResource AppBackgroundGradientEndColor}" Offset="1"/>
            </LinearGradientBrush>
        </Grid.Background>

        <!-- 返回按钮 -->
        <Button Name="BackButton"
                Width="40"
                Height="40"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Margin="20"
                Background="Transparent"
                BorderThickness="0"
                Cursor="Hand">
            <materialIcons:MaterialIcon Kind="ArrowLeft"
                                        Width="24"
                                        Height="24"
                                        Foreground="{DynamicResource IconBrush}" />

            <!-- 按钮悬停效果 -->
            <Button.Styles>
                <Style Selector="Button:pointerover">
                    <Setter Property="Background" Value="{DynamicResource ButtonHoverBackgroundBrush}"/>
                </Style>
                <Style Selector="Button:pressed">
                    <Setter Property="Background" Value="{DynamicResource ButtonHoverBackgroundBrush}"/>
                </Style>
            </Button.Styles>
        </Button>

        <!-- 设置内容 -->
        <ScrollViewer HorizontalAlignment="Stretch"
                      VerticalAlignment="Stretch"
                      Margin="80,80,20,80"
                      HorizontalScrollBarVisibility="Disabled"
                      VerticalScrollBarVisibility="Auto">
            <StackPanel HorizontalAlignment="Center"
                        MaxWidth="1000"
                        Spacing="30">
                <!-- 标题 -->
                <TextBlock Name="TitleTextBlock"
                           Text="设置"
                           FontSize="32"
                           FontWeight="Bold"
                           Foreground="{DynamicResource PrimaryTextBrush}"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,20"/>

                <!-- 使用网格布局优化空间 -->
                <Grid ColumnDefinitions="*,20,*,20,*" RowDefinitions="Auto,Auto,Auto,Auto">
                    
                    <!-- 第一行 -->
                    <!-- 外观 -->
                    <Border Grid.Row="0" Grid.Column="0" VerticalAlignment="Top" Background="{DynamicResource CardBackgroundBrush}" BorderBrush="{DynamicResource CardBorderBrush}" BorderThickness="1" CornerRadius="8" Padding="20">
                        <StackPanel Spacing="15">
                            <TextBlock Text="外观" FontSize="18" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryTextBrush}"/>
                            <StackPanel Spacing="10">
                                <StackPanel Orientation="Horizontal" Spacing="10">
                                    <materialIcons:MaterialIcon Kind="Palette" Width="20" Height="20" Foreground="{DynamicResource SecondaryIconBrush}" VerticalAlignment="Center"/>
                                    <TextBlock Text="主题" FontSize="14" Foreground="{DynamicResource SecondaryTextBrush}" VerticalAlignment="Center"/>
                                </StackPanel>
                                <ComboBox Name="ThemeComboBox" Width="150" HorizontalAlignment="Left" SelectedIndex="0">
                                    <ComboBoxItem Content="深色模式"/>
                                    <ComboBoxItem Content="浅色模式"/>
                                    <ComboBoxItem Content="跟随系统"/>
                                </ComboBox>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- 语言 -->
                    <Border Grid.Row="0" Grid.Column="2" VerticalAlignment="Top" Background="{DynamicResource CardBackgroundBrush}" BorderBrush="{DynamicResource CardBorderBrush}" BorderThickness="1" CornerRadius="8" Padding="20">
                        <StackPanel Spacing="15">
                            <TextBlock Text="语言" FontSize="18" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryTextBrush}"/>
                            <StackPanel Spacing="10">
                                <StackPanel Orientation="Horizontal" Spacing="10">
                                    <materialIcons:MaterialIcon Kind="Translate" Width="20" Height="20" Foreground="{DynamicResource SecondaryIconBrush}" VerticalAlignment="Center"/>
                                    <TextBlock Text="界面语言" FontSize="14" Foreground="{DynamicResource SecondaryTextBrush}" VerticalAlignment="Center"/>
                                </StackPanel>
                                <ComboBox Name="LanguageComboBox" Width="150" HorizontalAlignment="Left" SelectedIndex="0">
                                    <ComboBoxItem Content="简体中文"/>
                                    <ComboBoxItem Content="English"/>
                                </ComboBox>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- 字体 -->
                    <Border Grid.Row="0" Grid.Column="4" VerticalAlignment="Top" Background="{DynamicResource CardBackgroundBrush}" BorderBrush="{DynamicResource CardBorderBrush}" BorderThickness="1" CornerRadius="8" Padding="20">
                        <StackPanel Spacing="15">
                            <TextBlock Text="字体" FontSize="18" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryTextBrush}"/>
                            <StackPanel Spacing="15">
                                <StackPanel Orientation="Horizontal" Spacing="10">
                                    <materialIcons:MaterialIcon Kind="FormatSize" Width="20" Height="20" Foreground="{DynamicResource SecondaryIconBrush}" VerticalAlignment="Center"/>
                                    <TextBlock Text="字体大小" FontSize="14" Foreground="{DynamicResource SecondaryTextBrush}" VerticalAlignment="Center"/>
                                </StackPanel>
                                <StackPanel Orientation="Horizontal" Spacing="6">
                                    <Button Name="FontSizeSmallButton" Content="小号" Width="50" Height="32" FontSize="11" Background="{DynamicResource ButtonBackgroundBrush}" Foreground="{DynamicResource PrimaryTextBrush}" BorderBrush="{DynamicResource ButtonBorderBrush}" BorderThickness="1" CornerRadius="4" Cursor="Hand"/>
                                    <Button Name="FontSizeDefaultButton" Content="默认" Width="50" Height="32" FontSize="11" Background="#4A90E2" Foreground="#FFFFFF" BorderBrush="#4A90E2" BorderThickness="1" CornerRadius="4" Cursor="Hand"/>
                                    <Button Name="FontSizeMediumButton" Content="中号" Width="50" Height="32" FontSize="11" Background="{DynamicResource ButtonBackgroundBrush}" Foreground="{DynamicResource PrimaryTextBrush}" BorderBrush="{DynamicResource ButtonBorderBrush}" BorderThickness="1" CornerRadius="4" Cursor="Hand"/>
                                    <Button Name="FontSizeLargeButton" Content="大号" Width="50" Height="32" FontSize="11" Background="{DynamicResource ButtonBackgroundBrush}" Foreground="{DynamicResource PrimaryTextBrush}" BorderBrush="{DynamicResource ButtonBorderBrush}" BorderThickness="1" CornerRadius="4" Cursor="Hand"/>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                    
                    <!-- 第二行 -->
                    <!-- 关于 -->
                    <Border Grid.Row="1" Grid.Column="0" VerticalAlignment="Top" Margin="0,20,0,0" Background="{DynamicResource CardBackgroundBrush}" BorderBrush="{DynamicResource CardBorderBrush}" BorderThickness="1" CornerRadius="8" Padding="20">
                        <StackPanel Spacing="15">
                            <TextBlock Text="关于" FontSize="18" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryTextBrush}"/>
                            <StackPanel Spacing="8">
                                <StackPanel Orientation="Horizontal" Spacing="10">
                                    <materialIcons:MaterialIcon Kind="Information" Width="20" Height="20" Foreground="{DynamicResource SecondaryIconBrush}" VerticalAlignment="Center"/>
                                    <TextBlock Text="Lyxie" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryTextBrush}" VerticalAlignment="Center"/>
                                </StackPanel>
                                <TextBlock Name="VersionText" Text="版本 1.0.0" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}" Margin="30,0,0,0"/>
                                <TextBlock Name="DescriptionText" Text="基于 Avalonia UI 开发的现代化 AI 助手" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}" Margin="30,0,0,0" TextWrapping="Wrap"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- LLM API 设置 -->
                    <Border Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="3" VerticalAlignment="Top" Margin="0,20,0,0" Background="{DynamicResource CardBackgroundBrush}" BorderBrush="{DynamicResource CardBorderBrush}" BorderThickness="1" CornerRadius="8" Padding="20">
                        <StackPanel Spacing="15">
                            <TextBlock Text="LLM API 设置" FontSize="18" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryTextBrush}"/>
                            <!-- LLM API配置列表 -->
                            <Border Background="{DynamicResource CardBackgroundBrush}" BorderBrush="{DynamicResource CardBorderBrush}" BorderThickness="1" CornerRadius="4" Padding="10">
                                <StackPanel Spacing="10">
                                    <TextBlock Text="配置列表" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryTextBrush}"/>
                                    <StackPanel Name="LlmConfigListPanel" Spacing="8">
                                        <!-- 配置项将在代码中动态添加 -->
                                    </StackPanel>
                                    <Button Name="AddLlmConfigButton" Content="添加新的 LLM 配置" HorizontalAlignment="Left" Margin="0,5,0,0" Padding="10,5" Background="{DynamicResource ButtonBackgroundBrush}" Foreground="{DynamicResource PrimaryTextBrush}" BorderBrush="{DynamicResource ButtonBorderBrush}" BorderThickness="1" CornerRadius="4" Cursor="Hand">
                                        <Button.Styles>
                                            <Style Selector="Button:pointerover">
                                                <Setter Property="Background" Value="{DynamicResource ButtonHoverBackgroundBrush}"/>
                                            </Style>
                                        </Button.Styles>
                                    </Button>
                                </StackPanel>
                            </Border>
                            <!-- 配置详情编辑区域 -->
                            <Border Name="ConfigDetailBorder" Background="{DynamicResource CardBackgroundBrush}" BorderBrush="{DynamicResource CardBorderBrush}" BorderThickness="1" CornerRadius="4" Padding="10" IsVisible="False">
                                <StackPanel Spacing="15">
                                    <TextBlock Text="配置详情" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryTextBrush}"/>
                                    <StackPanel Spacing="5">
                                        <TextBlock Text="配置名称" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        <TextBox Name="ConfigNameTextBox" Watermark="例如：OpenAI GPT-3.5"/>
                                    </StackPanel>
                                    <StackPanel Spacing="5">
                                        <TextBlock Text="API URL" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        <TextBox Name="ApiUrlTextBox" Watermark="例如：https://api.openai.com/v1/chat/completions"/>
                                    </StackPanel>
                                    <StackPanel Spacing="5">
                                        <TextBlock Text="API Key" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        <TextBox Name="ApiKeyTextBox" PasswordChar="•" Watermark="输入您的API密钥"/>
                                    </StackPanel>
                                    <StackPanel Spacing="5">
                                        <TextBlock Text="模型名称" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        <TextBox Name="ModelNameTextBox" Watermark="例如：gpt-3.5-turbo"/>
                                    </StackPanel>
                                    <StackPanel Spacing="5">
                                        <StackPanel Orientation="Horizontal" Spacing="5">
                                            <TextBlock Text="温度值" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                            <TextBlock Name="TemperatureValueText" Text="0.7" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        </StackPanel>
                                        <Slider Name="TemperatureSlider" Minimum="0" Maximum="2" Value="0.7" TickFrequency="0.1" IsSnapToTickEnabled="True"/>
                                    </StackPanel>
                                    <StackPanel Spacing="5">
                                        <TextBlock Text="最大令牌数" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        <NumericUpDown Name="MaxTokensNumericUpDown" Value="4096" Minimum="100" Maximum="999999999" Increment="100"/>
                                    </StackPanel>
                                    <StackPanel Spacing="8">
                                        <StackPanel Orientation="Horizontal" Spacing="10">
                                            <Button Name="TestApiButton" Content="测试 API" HorizontalAlignment="Left" Padding="15,8" Background="{DynamicResource ButtonBackgroundBrush}" Foreground="{DynamicResource PrimaryTextBrush}" BorderBrush="{DynamicResource ButtonBorderBrush}" BorderThickness="1" CornerRadius="4" Cursor="Hand">
                                                <Button.Styles>
                                                    <Style Selector="Button:pointerover">
                                                        <Setter Property="Background" Value="{DynamicResource ButtonHoverBackgroundBrush}"/>
                                                    </Style>
                                                </Button.Styles>
                                            </Button>
                                            <TextBlock Name="TestStatusTextBlock" VerticalAlignment="Center" FontSize="14" IsVisible="False"/>
                                        </StackPanel>
                                        <TextBlock Name="TestErrorTextBlock" TextWrapping="Wrap" MaxWidth="450" Foreground="#E57373" FontSize="12" IsVisible="False"/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Spacing="10">
                                        <Button Name="SaveLlmConfigButton" Content="保存配置" HorizontalAlignment="Left" Margin="0,10,0,0" Padding="15,8" Background="#4A90E2" Foreground="#FFFFFF" BorderBrush="#4A90E2" BorderThickness="1" CornerRadius="4" Cursor="Hand">
                                            <Button.Styles>
                                                <Style Selector="Button:pointerover">
                                                    <Setter Property="Background" Value="#3A80D2"/>
                                                    <Setter Property="BorderBrush" Value="#3A80D2"/>
                                                </Style>
                                            </Button.Styles>
                                        </Button>
                                        <Button Name="CancelLlmConfigButton" Content="取消" HorizontalAlignment="Left" Margin="0,10,0,0" Padding="15,8" Background="{DynamicResource ButtonBackgroundBrush}" Foreground="{DynamicResource PrimaryTextBrush}" BorderBrush="{DynamicResource ButtonBorderBrush}" BorderThickness="1" CornerRadius="4" Cursor="Hand">
                                            <Button.Styles>
                                                <Style Selector="Button:pointerover">
                                                    <Setter Property="Background" Value="{DynamicResource ButtonHoverBackgroundBrush}"/>
                                                </Style>
                                            </Button.Styles>
                                        </Button>
                                        <TextBlock Name="SaveStatusTextBlock" Text="" VerticalAlignment="Center" Margin="10,10,0,0" FontSize="14" Foreground="#4A90E2" IsVisible="False"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Border>
                    
                    <!-- 第三行 -->
                    <!-- TTS API 设置 -->
                    <Border Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="5" VerticalAlignment="Top" Margin="0,20,0,0" Background="{DynamicResource CardBackgroundBrush}" BorderBrush="{DynamicResource CardBorderBrush}" BorderThickness="1" CornerRadius="8" Padding="20">
                        <StackPanel Spacing="15">
                            <TextBlock Text="TTS API 设置" FontSize="18" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryTextBrush}"/>
                            <!-- TTS API配置列表 -->
                            <Border Background="{DynamicResource CardBackgroundBrush}" BorderBrush="{DynamicResource CardBorderBrush}" BorderThickness="1" CornerRadius="4" Padding="10">
                                <StackPanel Spacing="10">
                                    <TextBlock Text="配置列表" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryTextBrush}"/>
                                    <StackPanel Name="TtsConfigListPanel" Spacing="8">
                                        <!-- TTS配置项将在代码中动态添加 -->
                                    </StackPanel>
                                    <Button Name="AddTtsConfigButton" Content="添加新的 TTS 配置" HorizontalAlignment="Left" Margin="0,5,0,0" Padding="10,5" Background="{DynamicResource ButtonBackgroundBrush}" Foreground="{DynamicResource PrimaryTextBrush}" BorderBrush="{DynamicResource ButtonBorderBrush}" BorderThickness="1" CornerRadius="4" Cursor="Hand">
                                        <Button.Styles>
                                            <Style Selector="Button:pointerover">
                                                <Setter Property="Background" Value="{DynamicResource ButtonHoverBackgroundBrush}"/>
                                            </Style>
                                        </Button.Styles>
                                    </Button>
                                </StackPanel>
                            </Border>
                            <!-- TTS配置详情编辑区域 -->
                            <Border Name="TtsConfigDetailBorder" Background="{DynamicResource CardBackgroundBrush}" BorderBrush="{DynamicResource CardBorderBrush}" BorderThickness="1" CornerRadius="4" Padding="10" IsVisible="False">
                                <StackPanel Spacing="15">
                                    <TextBlock Text="TTS配置详情" FontSize="14" FontWeight="SemiBold" Foreground="{DynamicResource PrimaryTextBrush}"/>
                                     <StackPanel Spacing="5">
                                        <TextBlock Text="配置名称" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        <TextBox Name="TtsConfigNameTextBox" Watermark="例如：Azure TTS"/>
                                    </StackPanel>
                                    <StackPanel Spacing="5">
                                        <TextBlock Text="TTS提供商" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        <ComboBox Name="TtsProviderComboBox" SelectedIndex="0">
                                            <ComboBoxItem Content="Azure"/>
                                            <ComboBoxItem Content="OpenAI"/>
                                            <ComboBoxItem Content="ElevenLabs"/>
                                            <ComboBoxItem Content="自定义API"/>
                                        </ComboBox>
                                    </StackPanel>
                                    <StackPanel Spacing="5">
                                        <TextBlock Text="API URL" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        <TextBox Name="TtsApiUrlTextBox" Watermark="例如：https://[region].tts.speech.microsoft.com/cognitiveservices/v1"/>
                                    </StackPanel>
                                    <StackPanel Spacing="5">
                                        <TextBlock Text="API Key" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        <TextBox Name="TtsApiKeyTextBox" PasswordChar="•" Watermark="输入您的TTS API密钥"/>
                                    </StackPanel>
                                    <StackPanel Spacing="5">
                                        <TextBlock Text="语音模型" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        <TextBox Name="TtsVoiceModelTextBox" Watermark="例如：zh-CN-XiaoxiaoNeural"/>
                                    </StackPanel>
                                    <StackPanel Name="TtsSynthesisModelPanel" Spacing="5" IsVisible="False">
                                        <TextBlock Text="合成模型ID" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        <TextBox Name="TtsSynthesisModelTextBox" Watermark="例如：eleven_multilingual_v2"/>
                                    </StackPanel>
                                    <StackPanel Spacing="5">
                                        <TextBlock Text="语言" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        <ComboBox Name="TtsLanguageComboBox" SelectedIndex="0">
                                            <ComboBoxItem Content="zh-CN"/>
                                            <ComboBoxItem Content="en-US"/>
                                            <ComboBoxItem Content="ja-JP"/>
                                            <ComboBoxItem Content="ko-KR"/>
                                        </ComboBox>
                                    </StackPanel>
                                    <StackPanel Spacing="5">
                                        <StackPanel Orientation="Horizontal" Spacing="5">
                                            <TextBlock Text="语速" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                            <TextBlock Name="TtsSpeedValueText" Text="1.0" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        </StackPanel>
                                        <Slider Name="TtsSpeedSlider" Minimum="0.25" Maximum="4.0" Value="1.0" TickFrequency="0.25" IsSnapToTickEnabled="True"/>
                                    </StackPanel>
                                    <StackPanel Spacing="5">
                                        <StackPanel Orientation="Horizontal" Spacing="5">
                                            <TextBlock Text="音调" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                            <TextBlock Name="TtsPitchValueText" Text="0" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        </StackPanel>
                                        <Slider Name="TtsPitchSlider" Minimum="-20" Maximum="20" Value="0" TickFrequency="1" IsSnapToTickEnabled="True"/>
                                    </StackPanel>
                                    <StackPanel Spacing="5">
                                        <StackPanel Orientation="Horizontal" Spacing="5">
                                            <TextBlock Text="音量" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                            <TextBlock Name="TtsVolumeValueText" Text="1.0" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        </StackPanel>
                                        <Slider Name="TtsVolumeSlider" Minimum="0.0" Maximum="2.0" Value="1.0" TickFrequency="0.1" IsSnapToTickEnabled="True"/>
                                    </StackPanel>
                                    <StackPanel Spacing="5">
                                        <TextBlock Text="音频格式" FontSize="12" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                        <ComboBox Name="TtsAudioFormatComboBox" SelectedIndex="0">
                                            <ComboBoxItem Content="MP3"/>
                                            <ComboBoxItem Content="WAV"/>
                                            <ComboBoxItem Content="OGG"/>
                                        </ComboBox>
                                    </StackPanel>
                                    <StackPanel Spacing="8">
                                        <StackPanel Orientation="Horizontal" Spacing="10">
                                            <Button Name="TestTtsApiButton" Content="测试 TTS API" HorizontalAlignment="Left" Padding="15,8" Background="{DynamicResource ButtonBackgroundBrush}" Foreground="{DynamicResource PrimaryTextBrush}" BorderBrush="{DynamicResource ButtonBorderBrush}" BorderThickness="1" CornerRadius="4" Cursor="Hand">
                                                <Button.Styles>
                                                    <Style Selector="Button:pointerover">
                                                        <Setter Property="Background" Value="{DynamicResource ButtonHoverBackgroundBrush}"/>
                                                    </Style>
                                                </Button.Styles>
                                            </Button>
                                            <TextBlock Name="TestTtsStatusTextBlock" VerticalAlignment="Center" FontSize="14" IsVisible="False"/>
                                        </StackPanel>
                                        <TextBlock Name="TestTtsErrorTextBlock" TextWrapping="Wrap" MaxWidth="450" Foreground="#E57373" FontSize="12" IsVisible="False"/>
                                    </StackPanel>
                                    <StackPanel Orientation="Horizontal" Spacing="10">
                                        <Button Name="SaveTtsConfigButton" Content="保存TTS配置" HorizontalAlignment="Left" Margin="0,10,0,0" Padding="15,8" Background="#4A90E2" Foreground="#FFFFFF" BorderBrush="#4A90E2" BorderThickness="1" CornerRadius="4" Cursor="Hand">
                                            <Button.Styles>
                                                <Style Selector="Button:pointerover">
                                                    <Setter Property="Background" Value="#3A80D2"/>
                                                    <Setter Property="BorderBrush" Value="#3A80D2"/>
                                                </Style>
                                            </Button.Styles>
                                        </Button>
                                        <Button Name="CancelTtsConfigButton" Content="取消" HorizontalAlignment="Left" Margin="0,10,0,0" Padding="15,8" Background="{DynamicResource ButtonBackgroundBrush}" Foreground="{DynamicResource PrimaryTextBrush}" BorderBrush="{DynamicResource ButtonBorderBrush}" BorderThickness="1" CornerRadius="4" Cursor="Hand">
                                            <Button.Styles>
                                                <Style Selector="Button:pointerover">
                                                    <Setter Property="Background" Value="{DynamicResource ButtonHoverBackgroundBrush}"/>
                                                </Style>
                                            </Button.Styles>
                                        </Button>
                                        <TextBlock Name="SaveTtsStatusTextBlock" Text="" VerticalAlignment="Center" Margin="10,10,0,0" FontSize="14" Foreground="#4A90E2" IsVisible="False"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Border>
                    
                    <!-- MCP 服务设置 -->
                    <Border Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="5" VerticalAlignment="Top" Margin="0,20,0,0"
                            Background="{DynamicResource CardBackgroundBrush}"
                            BorderBrush="{DynamicResource CardBorderBrush}" BorderThickness="1" CornerRadius="8" Padding="20">
                        <StackPanel Spacing="15">
                            <Grid ColumnDefinitions="*, Auto">
                                <TextBlock Text="MCP 工具" FontSize="18" FontWeight="SemiBold"
                                           Foreground="{DynamicResource PrimaryTextBrush}"
                                           VerticalAlignment="Center"/>
                                <Button Name="EditMcpFileButton" Grid.Column="1"
                                        Content="统一编辑"
                                        Click="EditMcpFileButton_Click"
                                        Padding="10,5"
                                        IsEnabled="{Binding !IsEditingMcpFile}"/>
                            </Grid>

                            <!-- View for listing services -->
                            <Panel IsVisible="{Binding !IsEditingMcpFile}">
                                <StackPanel Spacing="10">


                                    <!-- Services List -->
                                    <ItemsControl ItemsSource="{Binding McpServices}">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate x:DataType="vm:McpServiceViewModel">
                                                <Border Background="{DynamicResource CardBackgroundBrush}"
                                                        BorderBrush="{DynamicResource CardBorderBrush}"
                                                        BorderThickness="0,0,0,1" Padding="0,15">
                                                    <Grid ColumnDefinitions="*,Auto,Auto" RowDefinitions="Auto,Auto" VerticalAlignment="Center">

                                                        <!-- Name and Status -->
                                                        <StackPanel Grid.Column="0" Grid.Row="0" Spacing="2" VerticalAlignment="Center">
                                                            <TextBlock Text="{Binding Name}" FontWeight="SemiBold" FontSize="16"
                                                                       Foreground="{DynamicResource PrimaryTextBrush}"/>
                                                            <TextBlock Text="{Binding StatusText}" FontSize="12"
                                                                       Foreground="{DynamicResource SecondaryTextBrush}"/>
                                                        </StackPanel>

                                                        <!-- Validation Details -->
                                                        <StackPanel Grid.Column="0" Grid.Row="1" Spacing="2" Margin="0,5,0,0"
                                                                    IsVisible="{Binding IsEnabled}">
                                                            <TextBlock Text="{Binding ValidationStatusText}" FontSize="11"
                                                                       Foreground="{DynamicResource SecondaryTextBrush}"/>
                                                            <TextBlock Text="{Binding ErrorMessage}" FontSize="10"
                                                                       Foreground="#E57373" TextWrapping="Wrap"
                                                                       IsVisible="{Binding ErrorMessage, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
                                                            <TextBlock Text="{Binding LastCheckedText}" FontSize="10"
                                                                       Foreground="{DynamicResource SecondaryTextBrush}"
                                                                       IsVisible="{Binding LastCheckedText, Converter={x:Static StringConverters.IsNotNullOrEmpty}}"/>
                                                        </StackPanel>



                                                        <!-- Availability Dot -->
                                                        <Ellipse Grid.Column="1" Grid.RowSpan="2" Width="12" Height="12" 
                                                                 Margin="10,0" VerticalAlignment="Center"
                                                                 Fill="{Binding IsAvailable, Converter={StaticResource BoolToBrushConverter}, ConverterParameter='#4CAF50,#FFC107'}"/>

                                                        <!-- Toggle Switch -->
                                                        <ToggleSwitch Grid.Column="2" Grid.RowSpan="2" IsChecked="{Binding IsEnabled}"
                                                                      OnContent="" OffContent="" Margin="10,0,0,0"
                                                                      VerticalAlignment="Center"/>
                                                    </Grid>
                                                </Border>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </StackPanel>
                                
                                <TextBlock Text="MCP 工具配置为空，请点击“统一编辑”添加配置。" 
                                           FontSize="14"
                                           Foreground="{DynamicResource SecondaryTextBrush}"
                                           IsVisible="{Binding !IsMcpConfigPresent}"
                                           HorizontalAlignment="Center" VerticalAlignment="Center" Margin="0,30"/>
                            </Panel>

                            <!-- View for editing the entire JSON file -->
                            <Panel IsVisible="{Binding IsEditingMcpFile}">
                                <StackPanel Spacing="10">
                                    <TextBlock Text="编辑 mcp_settings.json" FontSize="14" Foreground="{DynamicResource SecondaryTextBrush}"/>
                                    <TextBox Text="{Binding McpFileContent, Mode=TwoWay}"
                                             AcceptsReturn="True" TextWrapping="Wrap" Height="250"
                                             FontFamily="Cascadia Mono, Consolas, monospace"
                                             Watermark="在此处输入或粘贴 MCP JSON 配置..."/>

                                    <TextBlock Text="{Binding McpFileValidationError}" Foreground="#E57373" FontSize="12" 
                                               IsVisible="{Binding McpFileHasError}" TextWrapping="Wrap"/>

                                    <StackPanel Orientation="Horizontal" Spacing="10" Margin="0,10,0,0">
                                        <Button Name="SaveMcpFileButton" Content="保存" Click="SaveMcpFileButton_Click" Padding="15,5"/>
                                        <Button Name="CancelMcpFileButton" Content="取消" Click="CancelMcpFileButton_Click" Padding="15,5" Background="Transparent" BorderBrush="{DynamicResource ButtonBorderBrush}" BorderThickness="1"/>
                                    </StackPanel>
                                </StackPanel>
                            </Panel>
                        </StackPanel>
                    </Border>
                </Grid>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>